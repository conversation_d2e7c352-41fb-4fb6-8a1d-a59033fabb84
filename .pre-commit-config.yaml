# Pre-commit hooks配置
# 使用 'pre-commit install' 安装hooks
# 使用 'pre-commit run --all-files' 运行所有hooks

repos:
  # Ruff - 现代化的Python linter和formatter
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.9
    hooks:
      # 运行ruff linter
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
        types_or: [python, pyi, jupyter]
      
      # 运行ruff formatter
      - id: ruff-format
        types_or: [python, pyi, jupyter]

  # MyPy - 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-PyYAML, types-requests]
        args: [--ignore-missing-imports]
        exclude: ^(tests/|scripts/|docs/)

  # 通用文件检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      # 检查文件大小
      - id: check-added-large-files
        args: ['--maxkb=1000']
      
      # 检查合并冲突标记
      - id: check-merge-conflict
      
      # 检查YAML语法
      - id: check-yaml
        exclude: ^(.github/|docs/).*\.ya?ml$
      
      # 检查TOML语法
      - id: check-toml
      
      # 检查JSON语法
      - id: check-json
      
      # 检查文件名
      - id: check-case-conflict
      
      # 移除文件末尾空白
      - id: trailing-whitespace
        exclude: ^.*\.(md|rst)$
      
      # 确保文件以换行符结尾
      - id: end-of-file-fixer
        exclude: ^.*\.(md|rst)$
      
      # 修复混合行结束符
      - id: mixed-line-ending
        args: ['--fix=lf']

  # Jupyter notebook清理
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.1
    hooks:
      - id: nbqa-ruff
        args: [--fix, --ignore=E402,F401]
      - id: nbqa-ruff-format

  # 安全检查
  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: [-r, src/]
        exclude: ^tests/

  # 文档检查
  - repo: https://github.com/pycqa/doc8
    rev: v1.1.1
    hooks:
      - id: doc8
        args: [--max-line-length=100]
        files: \.rst$

# 全局配置
default_stages: [commit]
fail_fast: false

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
