"""
Scheduler registration for LLIE framework.

This module registers common PyTorch learning rate schedulers with the registry system.
"""

import torch.optim.lr_scheduler as lr_scheduler
from .registry import register_scheduler


# Register common PyTorch schedulers
@register_scheduler("StepLR")
class StepLR(lr_scheduler.StepLR):
    """StepLR scheduler."""

    pass


@register_scheduler("MultiStepLR")
class MultiStepLR(lr_scheduler.MultiStepLR):
    """MultiStepLR scheduler."""

    pass


@register_scheduler("ExponentialLR")
class ExponentialLR(lr_scheduler.ExponentialLR):
    """ExponentialLR scheduler."""

    pass


@register_scheduler("CosineAnnealingLR")
class CosineAnnealingLR(lr_scheduler.CosineAnnealingLR):
    """CosineAnnealingLR scheduler."""

    pass


@register_scheduler("ReduceLROnPlateau")
class ReduceLROnPlateau(lr_scheduler.ReduceLROnPlateau):
    """ReduceLROnPlateau scheduler."""

    pass


@register_scheduler("CyclicLR")
class CyclicLR(lr_scheduler.CyclicLR):
    """CyclicLR scheduler."""

    pass


@register_scheduler("OneCycleLR")
class OneCycleLR(lr_scheduler.OneCycleLR):
    """OneCycleLR scheduler."""

    pass


@register_scheduler("CosineAnnealingWarmRestarts")
class CosineAnnealingWarmRestarts(lr_scheduler.CosineAnnealingWarmRestarts):
    """CosineAnnealingWarmRestarts scheduler."""

    pass


@register_scheduler("MultiplicativeLR")
class MultiplicativeLR(lr_scheduler.MultiplicativeLR):
    """MultiplicativeLR scheduler."""

    pass


@register_scheduler("LambdaLR")
class LambdaLR(lr_scheduler.LambdaLR):
    """LambdaLR scheduler."""

    pass


@register_scheduler("PolynomialLR")
class PolynomialLR(lr_scheduler.PolynomialLR):
    """PolynomialLR scheduler."""

    pass


@register_scheduler("LinearLR")
class LinearLR(lr_scheduler.LinearLR):
    """LinearLR scheduler."""

    pass
