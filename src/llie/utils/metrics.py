"""
Metrics for evaluating low-light image enhancement models.
Includes PSNR, SSIM, LPIPS and other image quality metrics.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Any, Optional
import numpy as np
from skimage.metrics import structural_similarity as sk_ssim
from skimage.metrics import peak_signal_noise_ratio as sk_psnr
import lpips

from .registry import register_metric


@register_metric("psnr")
class PSNR(nn.Module):
    """Peak Signal-to-Noise Ratio metric."""

    def __init__(self, max_val: float = 1.0, reduction: str = "mean"):
        """
        Args:
            max_val: Maximum possible pixel value.
            reduction: Reduction method ('mean', 'sum', 'none').
        """
        super().__init__()
        self.max_val = max_val
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Calculate PSNR between prediction and target.

        Args:
            pred: Predicted images [B, C, H, W].
            target: Target images [B, C, H, W].

        Returns:
            PSNR values.
        """
        mse = F.mse_loss(pred, target, reduction="none")
        mse = mse.mean(dim=(1, 2, 3))  # Average over C, H, W

        psnr = 20 * torch.log10(self.max_val / torch.sqrt(mse + 1e-8))

        if self.reduction == "mean":
            return psnr.mean()
        elif self.reduction == "sum":
            return psnr.sum()
        else:
            return psnr


@register_metric("ssim")
class SSIM(nn.Module):
    """Structural Similarity Index Measure."""

    def __init__(
        self, window_size: int = 11, sigma: float = 1.5, k1: float = 0.01, k2: float = 0.03
    ):
        """
        Args:
            window_size: Size of the sliding window.
            sigma: Standard deviation for Gaussian kernel.
            k1, k2: SSIM parameters.
        """
        super().__init__()
        self.window_size = window_size
        self.sigma = sigma
        self.k1 = k1
        self.k2 = k2

        # Create Gaussian kernel
        self.register_buffer("kernel", self._create_kernel())

    def _create_kernel(self):
        """Create Gaussian kernel for SSIM computation."""
        coords = torch.arange(self.window_size, dtype=torch.float32)
        coords -= self.window_size // 2

        g = torch.exp(-(coords**2) / (2 * self.sigma**2))
        g /= g.sum()

        return g.outer(g).unsqueeze(0).unsqueeze(0)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Calculate SSIM between prediction and target.

        Args:
            pred: Predicted images [B, C, H, W].
            target: Target images [B, C, H, W].

        Returns:
            SSIM values.
        """
        batch_size, channels = pred.shape[:2]

        # Convert to grayscale if needed
        if channels == 3:
            # RGB to grayscale conversion
            weights = torch.tensor([0.299, 0.587, 0.114], device=pred.device)
            pred = (pred * weights.view(1, 3, 1, 1)).sum(dim=1, keepdim=True)
            target = (target * weights.view(1, 3, 1, 1)).sum(dim=1, keepdim=True)

        # Compute local means
        mu1 = F.conv2d(pred, self.kernel, padding=self.window_size // 2)
        mu2 = F.conv2d(target, self.kernel, padding=self.window_size // 2)

        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2

        # Compute local variances and covariance
        sigma1_sq = F.conv2d(pred * pred, self.kernel, padding=self.window_size // 2) - mu1_sq
        sigma2_sq = F.conv2d(target * target, self.kernel, padding=self.window_size // 2) - mu2_sq
        sigma12 = F.conv2d(pred * target, self.kernel, padding=self.window_size // 2) - mu1_mu2

        # SSIM computation
        c1 = (self.k1 * 1.0) ** 2  # Assuming max_val = 1.0
        c2 = (self.k2 * 1.0) ** 2

        numerator = (2 * mu1_mu2 + c1) * (2 * sigma12 + c2)
        denominator = (mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2)

        ssim_map = numerator / denominator
        return ssim_map.mean()


@register_metric("lpips")
class LPIPS(nn.Module):
    """Learned Perceptual Image Patch Similarity."""

    def __init__(self, network: str = "alex", spatial: bool = False):
        """
        Args:
            network: Network to use ('alex', 'vgg', 'squeeze').
            spatial: Whether to return spatial dimensions.
        """
        super().__init__()
        # 设置verbose=False以避免LPIPS初始化时的冗余输出
        self.lpips_fn = lpips.LPIPS(net=network, spatial=spatial, verbose=False)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Calculate LPIPS between prediction and target.

        Args:
            pred: Predicted images [B, C, H, W] in range [0, 1].
            target: Target images [B, C, H, W] in range [0, 1].

        Returns:
            LPIPS values.
        """
        # LPIPS expects images in range [-1, 1]
        pred_norm = pred * 2.0 - 1.0
        target_norm = target * 2.0 - 1.0

        return self.lpips_fn(pred_norm, target_norm).mean()


@register_metric("mae")
class MAE(nn.Module):
    """Mean Absolute Error."""

    def __init__(self, reduction: str = "mean"):
        super().__init__()
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        return F.l1_loss(pred, target, reduction=self.reduction)


@register_metric("mse")
class MSE(nn.Module):
    """Mean Squared Error."""

    def __init__(self, reduction: str = "mean"):
        super().__init__()
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        return F.mse_loss(pred, target, reduction=self.reduction)


class MetricTracker:
    """Track and compute metrics during training/validation."""

    def __init__(self, metrics: Dict[str, nn.Module]):
        """
        Args:
            metrics: Dictionary of metric name -> metric instance.
        """
        self.metrics = metrics
        self.reset()

    def reset(self):
        """Reset all tracked values."""
        self.values = {name: [] for name in self.metrics.keys()}
        self.count = 0

    def update(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, float]:
        """
        Update metrics with new predictions and targets.

        Args:
            pred: Predicted images.
            target: Target images.

        Returns:
            Dictionary of current metric values.
        """
        results = {}

        with torch.no_grad():
            for name, metric in self.metrics.items():
                try:
                    value = metric(pred, target)
                    if isinstance(value, torch.Tensor):
                        value = value.item()

                    self.values[name].append(value)
                    results[name] = value

                except Exception as e:
                    print(f"Warning: Failed to compute {name}: {e}")
                    results[name] = float("nan")

        self.count += 1
        return results

    def compute(self) -> Dict[str, float]:
        """
        Compute average metrics over all updates.

        Returns:
            Dictionary of average metric values.
        """
        if self.count == 0:
            return {name: 0.0 for name in self.metrics.keys()}

        return {name: np.mean(values) if values else 0.0 for name, values in self.values.items()}

    def get_best(self, mode: str = "max") -> Dict[str, float]:
        """
        Get best metric values.

        Args:
            mode: 'max' for higher is better, 'min' for lower is better.

        Returns:
            Dictionary of best metric values.
        """
        if self.count == 0:
            return {name: 0.0 for name in self.metrics.keys()}

        func = np.max if mode == "max" else np.min
        return {name: func(values) if values else 0.0 for name, values in self.values.items()}


def create_metric_tracker(metric_names: list, device: str = "cuda") -> MetricTracker:
    """
    Create a metric tracker with specified metrics.

    Args:
        metric_names: List of metric names to track.
        device: Device to place metrics on.

    Returns:
        Configured MetricTracker instance.
    """
    from .registry import METRICS

    metrics = {}
    for name in metric_names:
        try:
            metric = METRICS.build(name)
            if hasattr(metric, "to"):
                metric = metric.to(device)
            metrics[name] = metric
        except Exception as e:
            print(f"Warning: Failed to create metric {name}: {e}")

    return MetricTracker(metrics)


# Utility functions for numpy/skimage compatibility
def calculate_psnr_numpy(img1: np.ndarray, img2: np.ndarray, max_val: float = 1.0) -> float:
    """Calculate PSNR using scikit-image."""
    return sk_psnr(img1, img2, data_range=max_val)


def calculate_ssim_numpy(img1: np.ndarray, img2: np.ndarray, max_val: float = 1.0) -> float:
    """Calculate SSIM using scikit-image."""
    if img1.ndim == 3 and img1.shape[2] == 3:
        return sk_ssim(img1, img2, data_range=max_val, multichannel=True, channel_axis=2)
    else:
        return sk_ssim(img1, img2, data_range=max_val)
