"""
实验日志管理器

统一管理实验过程中的所有日志输出，提供结构化、可读的日志记录功能。
"""

from typing import Dict, Any, Optional, List
from pathlib import Path
import json
import warnings
from datetime import datetime
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from loguru import logger

from .config_formatter import ConfigFormatter
from .progress_tracker import ProgressTracker


class ExperimentLogger:
    """
    实验日志管理器

    主要功能：
    1. 统一的实验日志输出格式
    2. 结构化的配置信息展示
    3. 进度跟踪和状态更新
    4. 结果汇总和错误处理
    5. 日志文件的统一管理

    设计原则：
    - 简单易用：提供直观的API接口
    - 信息清晰：重要信息突出显示，减少噪音
    - 易于调试：错误信息详细，便于问题定位
    """

    def __init__(self, experiment_name: str, output_dir: Path):
        """
        初始化实验日志管理器

        Args:
            experiment_name: 实验名称
            output_dir: 输出目录路径
        """
        self.experiment_name = experiment_name
        self.output_dir = Path(output_dir)
        self.console = Console()

        # 初始化子组件
        self.config_formatter = ConfigFormatter()
        self.progress_tracker = ProgressTracker()

        # 创建日志目录
        self.log_dir = self.output_dir / "logs"
        self.log_dir.mkdir(parents=True, exist_ok=True)

        # 设置日志文件
        self.log_file = self.log_dir / "experiment.log"
        self._setup_file_logging()

        # 设置警告捕获
        self._setup_warning_capture()

        # 实验开始时间
        self.start_time = datetime.now()

        logger.info(f"实验日志管理器初始化完成: {experiment_name}")

    def _setup_file_logging(self) -> None:
        """设置文件日志记录"""
        try:
            # 添加文件日志处理器
            logger.add(
                str(self.log_file),
                format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
                level="INFO",
                rotation="10 MB",
                retention="30 days",
                encoding="utf-8",
            )
            logger.info(f"日志文件设置完成: {self.log_file}")
        except Exception as e:
            logger.error(f"日志文件设置失败: {e}")

    def _setup_warning_capture(self) -> None:
        """设置警告信息捕获，将第三方库警告重定向到loguru"""
        def warning_handler(message, category, filename, lineno, file=None, line=None):
            """自定义警告处理器"""
            # 格式化警告信息
            warning_msg = f"{category.__name__}: {message}"
            if filename:
                warning_msg += f" (来源: {filename}:{lineno})"

            # 根据警告类型选择日志级别
            if issubclass(category, (DeprecationWarning, PendingDeprecationWarning)):
                logger.debug(f"⚠️ 弃用警告: {warning_msg}")
            elif issubclass(category, UserWarning):
                logger.warning(f"⚠️ 用户警告: {warning_msg}")
            else:
                logger.warning(f"⚠️ 系统警告: {warning_msg}")

        # 设置警告处理器
        warnings.showwarning = warning_handler
        logger.debug("警告捕获系统已启用")

    def log_experiment_start(self, config: Dict[str, Any]) -> None:
        """
        记录实验开始

        Args:
            config: 实验配置
        """
        # 显示实验开始横幅
        start_panel = Panel.fit(
            f"[bold green]🚀 实验开始[/bold green]\n\n"
            f"[bold]实验名称:[/bold] {self.experiment_name}\n"
            f"[bold]开始时间:[/bold] {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"[bold]输出目录:[/bold] {self.output_dir}",
            border_style="green",
            title="实验信息",
        )
        self.console.print("\n")
        self.console.print(start_panel)

        # 显示配置信息
        self.config_formatter.display_experiment_config(config)

        # 显示配置摘要表格
        summary_table = self.config_formatter.create_summary_table(config)
        self.console.print(summary_table)
        self.console.print("\n")

        # 保存配置文件
        self.config_formatter.save_config_files(config, self.output_dir)

        logger.info("实验配置记录完成")

    def log_training_start(self, total_epochs: int, epoch_steps: int) -> None:
        """
        记录训练开始

        Args:
            total_epochs: 总训练轮数
            epoch_steps: 每轮步数
        """
        self.progress_tracker.log_phase_start(
            "模型训练", f"{total_epochs} 轮训练，每轮 {epoch_steps} 步"
        )
        self.progress_tracker.start_training_progress(total_epochs, epoch_steps)

    def log_epoch_results(
        self,
        epoch: int,
        train_metrics: Dict[str, float],
        val_metrics: Optional[Dict[str, float]] = None,
    ) -> None:
        """
        记录训练轮次结果

        Args:
            epoch: 当前轮次
            train_metrics: 训练指标
            val_metrics: 验证指标（可选）
        """
        # 更新进度
        self.progress_tracker.update_epoch_progress(epoch, train_metrics, val_metrics)

        # 每10轮显示一次详细指标表格
        if (epoch + 1) % 10 == 0:
            metrics_table = self.progress_tracker.create_metrics_table(
                train_metrics, val_metrics, f"📊 第 {epoch + 1} 轮训练结果"
            )
            self.console.print(metrics_table)

    def log_training_complete(self, final_metrics: Dict[str, float]) -> None:
        """
        记录训练完成

        Args:
            final_metrics: 最终训练指标
        """
        self.progress_tracker.finish_training_progress()

        # 显示训练完成信息
        metrics_info = " | ".join([f"{k.upper()}: {v:.4f}" for k, v in final_metrics.items()])
        self.progress_tracker.log_phase_end("模型训练", metrics_info)

    def log_evaluation_start(self, total_samples: int) -> None:
        """
        记录评估开始

        Args:
            total_samples: 总样本数
        """
        self.progress_tracker.log_phase_start("模型评估", f"{total_samples} 个测试样本")
        self.progress_tracker.start_evaluation_progress(total_samples)

    def log_evaluation_progress(self, completed_samples: int) -> None:
        """
        记录评估进度

        Args:
            completed_samples: 已完成样本数
        """
        self.progress_tracker.update_evaluation_progress(completed_samples)

    def log_evaluation_complete(
        self, final_metrics: Dict[str, float], model_analysis: Dict[str, Any]
    ) -> None:
        """
        记录评估完成

        Args:
            final_metrics: 最终评估指标
            model_analysis: 模型分析结果
        """
        self.progress_tracker.finish_evaluation_progress(final_metrics)

        # 显示详细评估结果
        self._display_evaluation_results(final_metrics, model_analysis)

        # 保存评估结果
        self._save_evaluation_results(final_metrics, model_analysis)

    def _display_evaluation_results(
        self, metrics: Dict[str, float], model_analysis: Dict[str, Any]
    ) -> None:
        """
        显示评估结果

        Args:
            metrics: 评估指标
            model_analysis: 模型分析结果
        """
        # 创建评估结果表格
        results_table = Table(title="🎯 最终评估结果", show_header=True, header_style="bold green")
        results_table.add_column("指标类型", style="cyan", no_wrap=True)
        results_table.add_column("指标名称", style="blue", no_wrap=True)
        results_table.add_column("数值", style="green", justify="right")
        results_table.add_column("性能评价", style="yellow")

        # 添加图像质量指标
        for metric_name, value in metrics.items():
            performance = self._evaluate_metric_performance(metric_name, value)
            results_table.add_row("图像质量", metric_name.upper(), f"{value:.4f}", performance)

        # 添加模型复杂度指标
        if "parameters" in model_analysis:
            params = model_analysis["parameters"]
            total_params = params.get("total_parameters", 0)
            results_table.add_row(
                "模型复杂度", "参数量", f"{total_params:,}", self._evaluate_params(total_params)
            )

        if "flops" in model_analysis:
            flops = model_analysis["flops"].get("total_flops", 0)
            flops_str = f"{flops / 1e9:.2f}G" if flops > 1e9 else f"{flops / 1e6:.2f}M"
            results_table.add_row("计算复杂度", "FLOPS", flops_str, self._evaluate_flops(flops))

        if "timing" in model_analysis:
            timing = model_analysis["timing"]
            mean_time = timing.get("mean_time", 0) * 1000  # 转换为毫秒
            results_table.add_row(
                "推理性能", "平均耗时", f"{mean_time:.2f} ms", self._evaluate_timing(mean_time)
            )

        self.console.print("\n")
        self.console.print(results_table)
        self.console.print("\n")

    def _evaluate_metric_performance(self, metric_name: str, value: float) -> str:
        """评估指标性能"""
        if metric_name.lower() == "psnr":
            if value >= 25:
                return "优秀"
            elif value >= 20:
                return "良好"
            elif value >= 15:
                return "一般"
            else:
                return "较差"
        elif metric_name.lower() == "ssim":
            if value >= 0.9:
                return "优秀"
            elif value >= 0.8:
                return "良好"
            elif value >= 0.7:
                return "一般"
            else:
                return "较差"
        elif metric_name.lower() in ["mae", "mse"]:
            if value <= 0.05:
                return "优秀"
            elif value <= 0.1:
                return "良好"
            elif value <= 0.2:
                return "一般"
            else:
                return "较差"
        elif metric_name.lower() == "lpips":
            if value <= 0.1:
                return "优秀"
            elif value <= 0.2:
                return "良好"
            elif value <= 0.4:
                return "一般"
            else:
                return "较差"
        return "未知"

    def _evaluate_params(self, params: int) -> str:
        """评估参数量"""
        if params < 1e6:
            return "轻量级"
        elif params < 10e6:
            return "中等"
        elif params < 50e6:
            return "较大"
        else:
            return "大型"

    def _evaluate_flops(self, flops: float) -> str:
        """评估计算量"""
        if flops < 1e9:
            return "低计算量"
        elif flops < 10e9:
            return "中等计算量"
        elif flops < 50e9:
            return "高计算量"
        else:
            return "极高计算量"

    def _evaluate_timing(self, time_ms: float) -> str:
        """评估推理时间"""
        if time_ms < 10:
            return "极快"
        elif time_ms < 50:
            return "快速"
        elif time_ms < 100:
            return "中等"
        else:
            return "较慢"

    def _save_evaluation_results(
        self, metrics: Dict[str, float], model_analysis: Dict[str, Any]
    ) -> None:
        """
        保存评估结果到文件

        Args:
            metrics: 评估指标
            model_analysis: 模型分析结果
        """
        try:
            results = {
                "experiment_name": self.experiment_name,
                "timestamp": datetime.now().isoformat(),
                "metrics": metrics,
                "model_analysis": ConfigFormatter.safe_serialize(model_analysis),
            }

            results_file = self.output_dir / "evaluation_results.json"
            with open(results_file, "w", encoding="utf-8") as f:
                json.dump(results, f, indent=2, ensure_ascii=False)

            logger.info(f"评估结果已保存: {results_file}")

        except Exception as e:
            logger.error(f"评估结果保存失败: {e}")

    def log_error(self, error_msg: str, exception: Optional[Exception] = None) -> None:
        """
        记录错误信息

        Args:
            error_msg: 错误消息
            exception: 异常对象（可选）
        """
        error_panel = Panel.fit(
            f"[bold red]❌ 错误发生[/bold red]\n\n"
            f"[bold]错误信息:[/bold] {error_msg}\n"
            f"[bold]发生时间:[/bold] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            border_style="red",
            title="错误报告",
        )
        self.console.print(error_panel)

        logger.error(error_msg)
        if exception:
            logger.exception("详细错误信息:")

    def log_experiment_complete(self) -> None:
        """记录实验完成"""
        end_time = datetime.now()
        duration = end_time - self.start_time

        complete_panel = Panel.fit(
            f"[bold green]🎉 实验完成[/bold green]\n\n"
            f"[bold]实验名称:[/bold] {self.experiment_name}\n"
            f"[bold]完成时间:[/bold] {end_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"[bold]总用时:[/bold] {duration}\n"
            f"[bold]输出目录:[/bold] {self.output_dir}",
            border_style="green",
            title="实验总结",
        )
        self.console.print("\n")
        self.console.print(complete_panel)

        logger.success(f"实验 '{self.experiment_name}' 成功完成，用时 {duration}")
