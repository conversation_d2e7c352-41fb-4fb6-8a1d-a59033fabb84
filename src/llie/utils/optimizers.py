"""
Optimizer registration for LLIE framework.

This module registers common PyTorch optimizers with the registry system.
"""

import torch.optim as optim
from .registry import register_optimizer


# Register common PyTorch optimizers
@register_optimizer("Adam")
class Adam(optim.Adam):
    """Adam optimizer."""

    pass


@register_optimizer("AdamW")
class AdamW(optim.AdamW):
    """AdamW optimizer."""

    pass


@register_optimizer("SGD")
class SGD(optim.SGD):
    """SGD optimizer."""

    pass


@register_optimizer("RMSprop")
class RMSprop(optim.RMSprop):
    """RMSprop optimizer."""

    pass


@register_optimizer("Adagrad")
class Adagrad(optim.Adagrad):
    """Adagrad optimizer."""

    pass


@register_optimizer("Adadelta")
class Adadelta(optim.Adadelta):
    """Adadelta optimizer."""

    pass


@register_optimizer("Adamax")
class Adamax(optim.Adamax):
    """Adamax optimizer."""

    pass


@register_optimizer("ASGD")
class ASGD(optim.ASGD):
    """ASGD optimizer."""

    pass


@register_optimizer("LBFGS")
class LBFGS(optim.LBFGS):
    """LBFGS optimizer."""

    pass


@register_optimizer("Rprop")
class Rprop(optim.Rprop):
    """Rprop optimizer."""

    pass


@register_optimizer("SparseAdam")
class SparseAdam(optim.SparseAdam):
    """SparseAdam optimizer."""

    pass
