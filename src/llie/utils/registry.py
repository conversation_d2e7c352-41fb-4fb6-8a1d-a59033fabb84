"""
Registry pattern implementation for plug-and-play components.
This module provides decorators and registries for models, datasets, losses, etc.
"""

import functools
from typing import Dict, Type, Any, Callable, Optional, Union
import inspect
from loguru import logger


class Registry:
    """
    A registry to map strings to classes.

    Args:
        name: Registry name for identification

    Attributes:
        name: Registry name
        _module_dict: Dictionary mapping string names to classes
    """

    def __init__(self, name: str) -> None:
        self.name = name
        self._module_dict: Dict[str, Any] = {}

    def __len__(self) -> int:
        return len(self._module_dict)

    def __contains__(self, key: str) -> bool:
        return key in self._module_dict

    def __repr__(self) -> str:
        format_str = (
            f"{self.__class__.__name__}(name={self.name}, items={list(self._module_dict.keys())})"
        )
        return format_str

    @property
    def module_dict(self) -> Dict[str, Any]:
        return self._module_dict

    def get(self, key: str) -> Optional[Any]:
        """Get the registry record.

        Args:
            key: The class name in string format.

        Returns:
            The corresponding class or None if not found.
        """
        return self._module_dict.get(key, None)

    def register(self, name: Optional[str] = None) -> Callable:
        """Simplified register decorator for backward compatibility.

        Args:
            name: Name to register the class under. If None, uses class name.

        Returns:
            Decorator function.
        """

        def decorator(cls: Type) -> Type:
            register_name = name or cls.__name__
            if register_name in self._module_dict:
                logger.warning(f"Overriding {self.name} '{register_name}'")

            self._module_dict[register_name] = cls
            logger.debug(f"Registered {self.name}: {register_name}")
            return cls

        return decorator

    def register_module(
        self, name: Optional[str] = None, force: bool = False, module: Optional[Type] = None
    ) -> Union[Type, callable]:
        """Register a module.

        A record will be added to `self._module_dict`, whose key is the class
        name or the specified name, and value is the class itself.
        It can be used as a decorator or a normal function.

        Args:
            name: The module name to be registered. If not specified, the class
                name will be used.
            force: Whether to override an existing registered module if exists.
            module: Module class or function to be registered.

        Examples:
            >>> # Use as decorator
            >>> @MODELS.register_module()
            >>> class ResNet:
            >>>     pass

            >>> # Use as function with name
            >>> @MODELS.register_module(name='mynet')
            >>> class CustomNet:
            >>>     pass

        Returns:
            The registered module class.
        """

        if not isinstance(force, bool):
            raise TypeError(f"force must be a boolean, but got {type(force)}")

        # Use as a decorator: @MODELS.register_module()
        if module is None:
            return functools.partial(self.register_module, name=name, force=force)

        # Use as a function: MODELS.register_module(module=SomeClass)
        if not inspect.isclass(module) and not inspect.isfunction(module):
            raise TypeError(f"module must be a class or function, but got {type(module)}")

        module_name = name if name is not None else module.__name__

        if module_name in self._module_dict and not force:
            logger.warning(f"{module_name} is already registered in {self.name}")
        else:
            self._module_dict[module_name] = module
            logger.debug(f"Registered {module_name} in {self.name}")

        return module

    def build(self, cfg: Union[Dict, str], *args, **kwargs) -> Any:
        """Build a module from config dict or string.

        Args:
            cfg: Config dict or string with module type and parameters.
                Must contain 'type' key specifying the module name.
            *args: Additional positional arguments.
            **kwargs: Additional keyword arguments.

        Returns:
            Built module instance.

        Examples:
            >>> model_cfg = {'type': 'ResNet', 'depth': 50}
            >>> model = MODELS.build(model_cfg)

            >>> # Or simply use string for modules without parameters
            >>> optimizer = OPTIMIZERS.build('Adam')
        """
        if isinstance(cfg, str):
            # Simple string type, no parameters
            cfg = {"type": cfg}
        elif not isinstance(cfg, dict):
            raise TypeError(f"cfg must be a dict or str, but got {type(cfg)}")

        if "type" not in cfg:
            raise KeyError('Config dict must contain "type" key')

        module_type = cfg.pop("type")
        if module_type not in self._module_dict:
            raise KeyError(
                f"{module_type} is not registered in {self.name}. "
                f"Available types: {list(self._module_dict.keys())}"
            )

        module_cls = self._module_dict[module_type]

        try:
            # Merge config parameters with additional kwargs
            merged_kwargs = {**cfg, **kwargs}
            return module_cls(*args, **merged_kwargs)
        except Exception as e:
            logger.error(f"Failed to build {module_type}: {str(e)}")
            raise


# Create global registries for different component types
MODELS = Registry("models")
DATASETS = Registry("datasets")
LOSSES = Registry("losses")
OPTIMIZERS = Registry("optimizers")
SCHEDULERS = Registry("schedulers")
METRICS = Registry("metrics")
TRANSFORMS = Registry("transforms")


# Convenience functions
def register_model(name: Optional[str] = None):
    """Register a model class."""
    return MODELS.register(name)


def register_dataset(name: Optional[str] = None):
    """Register a dataset class."""
    return DATASETS.register(name)


def register_loss(name: Optional[str] = None):
    """Register a loss function class."""
    return LOSSES.register(name)


def register_optimizer(name: Optional[str] = None):
    """Register an optimizer class."""
    return OPTIMIZERS.register(name)


def register_scheduler(name: Optional[str] = None):
    """Register a scheduler class."""
    return SCHEDULERS.register(name)


def register_transform(name: Optional[str] = None):
    """Register a transform class."""
    return TRANSFORMS.register(name)


def register_metric(name: Optional[str] = None):
    """Register a metric class."""
    return METRICS.register(name)


__all__ = [
    "Registry",
    "MODELS",
    "DATASETS",
    "LOSSES",
    "OPTIMIZERS",
    "SCHEDULERS",
    "METRICS",
    "TRANSFORMS",
    "register_model",
    "register_dataset",
    "register_loss",
    "register_optimizer",
    "register_scheduler",
    "register_transform",
    "register_metric",
]
