"""
Base task class for all LLIE operations.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any
from omegaconf import DictConfig
from loguru import logger


class BaseTask(ABC):
    """
    Base class for all tasks in the LLIE framework.

    Provides common functionality and interface for different tasks
    like training, evaluation, inference, etc.
    """

    def __init__(self, config: DictConfig):
        """
        Initialize task with configuration.

        Args:
            config: Hydra configuration object.
        """
        self.config = config
        self.name = self.__class__.__name__

        logger.info(f"Initializing task: {self.name}")

    @abstractmethod
    def setup(self):
        """Setup task-specific resources and components."""
        pass

    @abstractmethod
    def run(self) -> Dict[str, Any]:
        """
        Execute the task.

        Returns:
            Dictionary containing task results and metrics.
        """
        pass

    def cleanup(self):
        """Cleanup resources after task completion."""
        pass

    def __enter__(self):
        """Context manager entry."""
        self.setup()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.cleanup()

        if exc_type is not None:
            logger.error(f"Task {self.name} failed with exception: {exc_val}")
        else:
            logger.success(f"Task {self.name} completed successfully")

    def validate_config(self):
        """Validate task configuration."""
        # Override in subclasses for specific validation
        pass

    def get_output_dir(self):
        """Get output directory for task results."""
        from pathlib import Path

        return Path.cwd()  # Hydra manages the output directory
