"""
Modern loss functions for Low-Light Image Enhancement.

This module provides a comprehensive collection of loss functions optimized
for low-light image enhancement tasks, including perceptual losses,
structural losses, and specialized LLIE losses.
"""

from typing import Dict, Any, Optional, Tuple, List
import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from loguru import logger

try:
    import lpips

    LPIPS_AVAILABLE = True
except ImportError:
    LPIPS_AVAILABLE = False
    logger.warning("LPIPS not available. Install with: pip install lpips")

from ..utils import LOSSES, register_loss


@register_loss("CharbonnierLoss")
class CharbonnierLoss(nn.Module):
    """
    Charbonnier Loss (L1 variant with smooth approximation).

    A smooth approximation of L1 loss that provides better gradients near zero.
    Often used in image restoration tasks due to its robustness to outliers.

    Args:
        eps: Small constant for numerical stability. Default: 1e-6.
        reduction: Reduction method ('mean', 'sum', 'none'). Default: 'mean'.

    Mathematical formulation:
        L = sqrt((x - y)^2 + eps^2) - eps

    Example:
        >>> loss_fn = CharbonnierLoss(eps=1e-3)
        >>> pred = torch.randn(4, 3, 256, 256)
        >>> target = torch.randn(4, 3, 256, 256)
        >>> loss = loss_fn(pred, target)
    """

    def __init__(self, eps: float = 1e-6, reduction: str = "mean"):
        super().__init__()
        self.eps = eps
        self.reduction = reduction

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute Charbonnier loss between prediction and target.

        Args:
            pred: Predicted tensor [B, C, H, W].
            target: Target tensor [B, C, H, W].

        Returns:
            Computed loss value.
        """
        diff = pred - target
        loss = torch.sqrt(diff * diff + self.eps * self.eps)

        if self.reduction == "mean":
            return loss.mean()
        elif self.reduction == "sum":
            return loss.sum()
        else:
            return loss


class VGGPerceptualLoss(nn.Module):
    """
    VGG-based perceptual loss for enhanced visual quality.

    Uses pre-trained VGG features to compute perceptual distance
    between enhanced and ground truth images.

    Args:
        feature_layers: VGG layers to use for feature extraction.
        weights: Weights for each feature layer.
        normalize: Whether to normalize input images.
    """

    def __init__(
        self,
        feature_layers: List[str] = ["relu1_1", "relu2_1", "relu3_1", "relu4_1", "relu5_1"],
        weights: List[float] = [1.0, 1.0, 1.0, 1.0, 1.0],
        normalize: bool = True,
    ):
        super().__init__()

        self.feature_layers = feature_layers
        self.weights = weights
        self.normalize = normalize

        # Load pre-trained VGG16
        vgg = models.vgg16(pretrained=True).features
        self.vgg = nn.Sequential()

        # Extract specified layers
        layer_names = {
            "0": "conv1_1",
            "2": "conv1_2",
            "5": "conv2_1",
            "7": "conv2_2",
            "10": "conv3_1",
            "12": "conv3_2",
            "14": "conv3_3",
            "17": "conv4_1",
            "19": "conv4_2",
            "21": "conv4_3",
            "24": "conv5_1",
            "26": "conv5_2",
            "28": "conv5_3",
        }

        relu_layers = {
            "conv1_1": "relu1_1",
            "conv2_1": "relu2_1",
            "conv3_1": "relu3_1",
            "conv4_1": "relu4_1",
            "conv5_1": "relu5_1",
        }

        for i, layer in enumerate(vgg):
            name = layer_names.get(str(i), f"layer_{i}")
            self.vgg.add_module(name, layer)

            if name in relu_layers and relu_layers[name] in feature_layers:
                self.vgg.add_module(relu_layers[name], nn.ReLU(inplace=False))

        # Freeze VGG parameters
        for param in self.vgg.parameters():
            param.requires_grad = False

        # Normalization for ImageNet pre-trained model
        if normalize:
            self.register_buffer("mean", torch.tensor([0.485, 0.456, 0.406]).view(1, 3, 1, 1))
            self.register_buffer("std", torch.tensor([0.229, 0.224, 0.225]).view(1, 3, 1, 1))

    def _normalize(self, x: torch.Tensor) -> torch.Tensor:
        """Normalize input for VGG."""
        if self.normalize:
            return (x - self.mean) / self.std
        return x

    def _extract_features(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Extract VGG features."""
        features = {}
        x = self._normalize(x)

        for name, module in self.vgg.named_children():
            x = module(x)
            if name in self.feature_layers:
                features[name] = x

        return features

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute perceptual loss.

        Args:
            pred: Predicted image [B, 3, H, W].
            target: Ground truth image [B, 3, H, W].

        Returns:
            Perceptual loss value.
        """
        pred_features = self._extract_features(pred)
        target_features = self._extract_features(target)

        loss = 0.0
        for i, layer in enumerate(self.feature_layers):
            if layer in pred_features and layer in target_features:
                weight = self.weights[i] if i < len(self.weights) else 1.0
                loss += weight * F.mse_loss(pred_features[layer], target_features[layer])

        return loss


@register_loss("PerceptualLoss")
class PerceptualLoss(nn.Module):
    """
    Unified perceptual loss supporting both VGG and LPIPS.

    Args:
        loss_type: Type of perceptual loss ('vgg', 'lpips').
        weight: Weight for the perceptual loss.
    """

    def __init__(self, loss_type: str = "vgg", weight: float = 1.0):
        super().__init__()

        self.loss_type = loss_type
        self.weight = weight

        if loss_type == "vgg":
            self.perceptual_net = VGGPerceptualLoss()
        elif loss_type == "lpips" and LPIPS_AVAILABLE:
            self.perceptual_net = lpips.LPIPS(net="alex", verbose=False)
        else:
            raise ValueError(f"Unsupported perceptual loss type: {loss_type}")

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Compute perceptual loss."""
        return self.weight * self.perceptual_net(pred, target).mean()


@register_loss("SSIMLoss")
class SSIMLoss(nn.Module):
    """
    Structural Similarity Index (SSIM) loss.

    Args:
        window_size: Size of the Gaussian window.
        sigma: Standard deviation of the Gaussian window.
        reduction: Reduction method ('mean', 'sum', 'none').
    """

    def __init__(self, window_size: int = 11, sigma: float = 1.5, reduction: str = "mean"):
        super().__init__()

        self.window_size = window_size
        self.sigma = sigma
        self.reduction = reduction

        # Create Gaussian window
        self.register_buffer("window", self._create_window(window_size, sigma))

    def _gaussian(self, window_size: int, sigma: float) -> torch.Tensor:
        """Create 1D Gaussian kernel."""
        gauss = torch.exp(
            -torch.arange(window_size, dtype=torch.float).sub(window_size // 2).pow(2)
            / (2 * sigma**2)
        )
        return gauss / gauss.sum()

    def _create_window(self, window_size: int, sigma: float) -> torch.Tensor:
        """Create 2D Gaussian window."""
        _1D_window = self._gaussian(window_size, sigma).unsqueeze(1)
        _2D_window = _1D_window.mm(_1D_window.t()).float().unsqueeze(0).unsqueeze(0)
        return _2D_window

    def _ssim(self, img1: torch.Tensor, img2: torch.Tensor) -> torch.Tensor:
        """Compute SSIM between two images."""
        C1 = 0.01**2
        C2 = 0.03**2

        mu1 = F.conv2d(img1, self.window, padding=self.window_size // 2, groups=img1.shape[1])
        mu2 = F.conv2d(img2, self.window, padding=self.window_size // 2, groups=img2.shape[1])

        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2

        sigma1_sq = (
            F.conv2d(img1 * img1, self.window, padding=self.window_size // 2, groups=img1.shape[1])
            - mu1_sq
        )
        sigma2_sq = (
            F.conv2d(img2 * img2, self.window, padding=self.window_size // 2, groups=img2.shape[1])
            - mu2_sq
        )
        sigma12 = (
            F.conv2d(img1 * img2, self.window, padding=self.window_size // 2, groups=img1.shape[1])
            - mu1_mu2
        )

        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / (
            (mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2)
        )

        return ssim_map

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute SSIM loss.

        Args:
            pred: Predicted image [B, C, H, W].
            target: Ground truth image [B, C, H, W].

        Returns:
            SSIM loss (1 - SSIM).
        """
        # Expand window for multi-channel images
        if self.window.shape[1] != pred.shape[1]:
            window = self.window.expand(pred.shape[1], 1, self.window_size, self.window_size)
            self.register_buffer("window", window)

        ssim_val = self._ssim(pred, target)
        ssim_loss = 1 - ssim_val

        if self.reduction == "mean":
            return ssim_loss.mean()
        elif self.reduction == "sum":
            return ssim_loss.sum()
        else:
            return ssim_loss


@register_loss("EdgeLoss")
class EdgeLoss(nn.Module):
    """
    Edge-preserving loss using Sobel operators.

    Helps preserve fine details and edges in enhanced images.

    Args:
        weight: Weight for the edge loss.
    """

    def __init__(self, weight: float = 1.0):
        super().__init__()

        self.weight = weight

        # Sobel operators
        sobel_x = torch.tensor([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=torch.float32)
        sobel_y = torch.tensor([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=torch.float32)

        self.register_buffer("sobel_x", sobel_x.view(1, 1, 3, 3))
        self.register_buffer("sobel_y", sobel_y.view(1, 1, 3, 3))

    def _get_edges(self, img: torch.Tensor) -> torch.Tensor:
        """Extract edges using Sobel operators."""
        B, C, H, W = img.shape
        img_gray = img.mean(dim=1, keepdim=True)  # Convert to grayscale

        edges_x = F.conv2d(img_gray, self.sobel_x, padding=1)
        edges_y = F.conv2d(img_gray, self.sobel_y, padding=1)

        edges = torch.sqrt(edges_x**2 + edges_y**2)
        return edges

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute edge loss.

        Args:
            pred: Predicted image [B, C, H, W].
            target: Ground truth image [B, C, H, W].

        Returns:
            Edge preservation loss.
        """
        pred_edges = self._get_edges(pred)
        target_edges = self._get_edges(target)

        return self.weight * F.l1_loss(pred_edges, target_edges)


@register_loss("ColorLoss")
class ColorLoss(nn.Module):
    """
    Color consistency loss in LAB color space.

    Ensures color fidelity in enhanced images by comparing
    in perceptually uniform LAB color space.

    Args:
        weight: Weight for the color loss.
    """

    def __init__(self, weight: float = 1.0):
        super().__init__()
        self.weight = weight

    def rgb_to_lab(self, rgb: torch.Tensor) -> torch.Tensor:
        """
        Convert RGB to LAB color space (approximation).

        Args:
            rgb: RGB image [B, 3, H, W] in range [0, 1].

        Returns:
            LAB image [B, 3, H, W].
        """
        # Simplified RGB to LAB conversion
        # This is an approximation for efficiency
        r, g, b = rgb[:, 0:1], rgb[:, 1:2], rgb[:, 2:3]

        # Convert to linear RGB
        r = torch.where(r > 0.04045, ((r + 0.055) / 1.055) ** 2.4, r / 12.92)
        g = torch.where(g > 0.04045, ((g + 0.055) / 1.055) ** 2.4, g / 12.92)
        b = torch.where(b > 0.04045, ((b + 0.055) / 1.055) ** 2.4, b / 12.92)

        # Convert to XYZ
        x = r * 0.4124 + g * 0.3576 + b * 0.1805
        y = r * 0.2126 + g * 0.7152 + b * 0.0722
        z = r * 0.0193 + g * 0.1192 + b * 0.9505

        # Convert to LAB
        x = x / 0.95047
        y = y / 1.00000
        z = z / 1.08883

        x = torch.where(x > 0.008856, x ** (1 / 3), (7.787 * x) + (16 / 116))
        y = torch.where(y > 0.008856, y ** (1 / 3), (7.787 * y) + (16 / 116))
        z = torch.where(z > 0.008856, z ** (1 / 3), (7.787 * z) + (16 / 116))

        L = (116 * y) - 16
        A = 500 * (x - y)
        B = 200 * (y - z)

        return torch.cat([L, A, B], dim=1)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute color consistency loss.

        Args:
            pred: Predicted image [B, C, H, W].
            target: Ground truth image [B, C, H, W].

        Returns:
            Color consistency loss.
        """
        pred_lab = self.rgb_to_lab(pred)
        target_lab = self.rgb_to_lab(target)

        return self.weight * F.l1_loss(pred_lab, target_lab)


@register_loss("CombinedLoss")
class CombinedLoss(nn.Module):
    """
    Combined loss function for comprehensive LLIE training.

    Combines multiple loss components for balanced optimization:
    - Pixel-wise loss (L1/L2)
    - Perceptual loss (VGG/LPIPS)
    - Structural loss (SSIM)
    - Edge preservation loss
    - Color consistency loss

    Args:
        pixel_weight: Weight for pixel-wise loss.
        perceptual_weight: Weight for perceptual loss.
        ssim_weight: Weight for SSIM loss.
        edge_weight: Weight for edge loss.
        color_weight: Weight for color loss.
        pixel_loss_type: Type of pixel loss ('l1', 'l2', 'smooth_l1').
        perceptual_type: Type of perceptual loss ('vgg', 'lpips').
    """

    def __init__(
        self,
        pixel_weight: float = 1.0,
        perceptual_weight: float = 0.1,
        ssim_weight: float = 0.1,
        edge_weight: float = 0.05,
        color_weight: float = 0.05,
        pixel_loss_type: str = "l1",
        perceptual_type: str = "vgg",
    ):
        super().__init__()

        self.pixel_weight = pixel_weight
        self.perceptual_weight = perceptual_weight
        self.ssim_weight = ssim_weight
        self.edge_weight = edge_weight
        self.color_weight = color_weight

        # Pixel-wise loss
        if pixel_loss_type == "l1":
            self.pixel_loss = nn.L1Loss()
        elif pixel_loss_type == "l2":
            self.pixel_loss = nn.MSELoss()
        elif pixel_loss_type == "smooth_l1":
            self.pixel_loss = nn.SmoothL1Loss()
        else:
            raise ValueError(f"Unsupported pixel loss type: {pixel_loss_type}")

        # Component losses
        if perceptual_weight > 0:
            self.perceptual_loss = PerceptualLoss(perceptual_type, weight=1.0)

        if ssim_weight > 0:
            self.ssim_loss = SSIMLoss()

        if edge_weight > 0:
            self.edge_loss = EdgeLoss(weight=1.0)

        if color_weight > 0:
            self.color_loss = ColorLoss(weight=1.0)

    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute combined loss.

        Args:
            pred: Predicted image [B, C, H, W].
            target: Ground truth image [B, C, H, W].

        Returns:
            Dictionary containing individual losses and total loss.
        """
        losses = {}

        # Pixel-wise loss
        losses["pixel"] = self.pixel_loss(pred, target)
        total_loss = self.pixel_weight * losses["pixel"]

        # Perceptual loss
        if self.perceptual_weight > 0:
            losses["perceptual"] = self.perceptual_loss(pred, target)
            total_loss += self.perceptual_weight * losses["perceptual"]

        # SSIM loss
        if self.ssim_weight > 0:
            losses["ssim"] = self.ssim_loss(pred, target)
            total_loss += self.ssim_weight * losses["ssim"]

        # Edge loss
        if self.edge_weight > 0:
            losses["edge"] = self.edge_loss(pred, target)
            total_loss += self.edge_weight * losses["edge"]

        # Color loss
        if self.color_weight > 0:
            losses["color"] = self.color_loss(pred, target)
            total_loss += self.color_weight * losses["color"]

        losses["total"] = total_loss
        return losses


# Utility function for building losses from config
def build_loss(config: Dict[str, Any]) -> nn.Module:
    """
    Build a loss function from configuration dictionary.

    Args:
        config: Loss configuration containing 'type' and other parameters.

    Returns:
        Initialized loss function.

    Example:
        >>> config = {
        ...     'type': 'CombinedLoss',
        ...     'pixel_weight': 1.0,
        ...     'perceptual_weight': 0.1,
        ...     'ssim_weight': 0.1
        ... }
        >>> loss_fn = build_loss(config)
    """
    from ...utils.registry import LOSSES

    loss_type = config.pop("type")
    return LOSSES.build(loss_type, **config)
